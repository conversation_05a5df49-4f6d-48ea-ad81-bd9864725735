import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../provider/device_provider.dart';
import '../widgets/bottom_action_button.dart';
import '../widgets/info_card.dart';
import '../widgets/search_input_row.dart';

class CustomerScreen extends StatefulWidget {
  CustomerScreen({super.key});

  @override
  State<CustomerScreen> createState() => _CustomerScreenState();
}

class _CustomerScreenState extends State<CustomerScreen> {
  TextEditingController customerController = TextEditingController();
  final List<MapEntry<String, Map<String, dynamic>>> dataEntries = [
    MapEntry("Customer Data", {"value": "John Doe", "icon": Icons.person}),
    MapEntry("Technician ID", {"value": "TECH789", "icon": Icons.badge}),
  ];

  @override
  Widget build(BuildContext context) {
    final deviceIdProvider = context.watch<DeviceProvider>();
    return Column(
      // padding: const EdgeInsets.all(20),
      children: [
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            itemCount: dataEntries.length + 1,
            itemBuilder: (context, index) {
              if (index == 0) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 10),
                  child: SearchInputRow(
                    title: 'Customer',
                    hintText: 'Enter Customer ID',
                    controller: customerController,
                    onPressed: () {
                      print(
                        'Search pressed with input: ${customerController.text}',
                      );
                    },
                  ),
                );
              } else {
                final entry = dataEntries[index - 1];
                return InfoCard(
                  title: entry.key,
                  value: entry.value['value'] as String,
                  iconData: entry.value['icon'] as IconData,
                );
              }
            },
          ),
        ),
        BottomActionButtons(
          onAssign: () => print('Assign pressed'),
          onGetDeviceId: () => deviceIdProvider.fetchDeviceId(),
          onExit: () => deviceIdProvider.exitApp(),
        ),
      ],
    );
  }
}
