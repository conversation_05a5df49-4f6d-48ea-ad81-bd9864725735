import 'package:flutter/material.dart';

import '../../core/constants/app_colors.dart';

class sliderWidget extends StatelessWidget {
  const sliderWidget({Key? key, required this.animate}) : super(key: key);
  final Animation<Offset> animate;

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animate,
      builder: (context, _) {
        return SlideTransition(
          position: animate,
          child: const Text(
            'TECHNICIAN MOBILE APP',
            style: TextStyle(
              color: AppColors.themeBlueColor,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
            textAlign: TextAlign.center,
          ),
        );
      },
    );
  }
}
