import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:technician_mobile_app/presentation/provider/device_provider.dart';
import 'package:technician_mobile_app/services/local_data.dart';
import 'package:technician_mobile_app/technician_mobile_app.dart';

import 'data/datasources/device_info_local_data_source.dart';
import 'data/repositories/device_repository_impl.dart';
import 'domain/usecases/get_device_id.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  CacheHelper cacheHelper = CacheHelper();
  await cacheHelper.init();
  final deviceInfoPlugin = DeviceInfoPlugin();
  final localDataSource = DeviceInfoLocalDataSourceImpl(deviceInfoPlugin);
  final repository = DeviceRepositoryImpl(localDataSource);
  final getDeviceIdUseCase = GetDeviceId(repository);
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => DeviceProvider(getDeviceIdUseCase, cacheHelper),
        ),
      ],
      child: const TechnicianMobileFlutter(),
    ),
  );
}
