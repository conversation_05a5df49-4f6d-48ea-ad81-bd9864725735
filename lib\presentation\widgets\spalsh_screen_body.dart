import 'package:flutter/material.dart';
import 'package:technician_mobile_app/core/constants/app_images.dart';

import '../screens/home_screen.dart';

class SplashScreenBody extends StatefulWidget {
  const SplashScreenBody({Key? key}) : super(key: key);

  @override
  State<SplashScreenBody> createState() => _SplashScreenBodyState();
}

class _SplashScreenBodyState extends State<SplashScreenBody>
    with SingleTickerProviderStateMixin {
  late AnimationController controler;
  late Animation<Offset> animate;

  @override
  void initState() {
    super.initState();
    controler = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    );
    animate = Tween<Offset>(
      begin: const Offset(0, 2),
      end: Offset.zero,
    ).animate(controler);
    controler.forward();

    Future.delayed(const Duration(seconds: 2), () {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (_) {
            return HomeScreen();
          },
        ),
      );
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    controler.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Align(
          alignment: Alignment.center,
          // child: Text(
          //   'Technician mobile app',
          //   style: TextStyle(
          //     color: Colors.white,
          //     fontSize: 40,
          //     fontWeight: FontWeight.w900,
          //     shadows: <Shadow>[
          //       Shadow(
          //         offset: Offset(2.0, 4.0), // offset of shadow
          //         blurRadius: 1.0, // blur radius of shadow
          //         color: Colors.black,
          //
          //         // color of shadow
          //       ),
          //     ],
          //   ),
          // ),
          child: Image.asset(AppImages.logoImage, width: 200, height: 200),
        ),
        // verticalSpace(5),
        // sliderWidget(animate: animate),
      ],
    );
  }
}
