import 'package:flutter/material.dart';
import 'package:technician_mobile_app/core/constants/app_colors.dart';

class InfoCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData iconData;

  const InfoCard({
    Key? key,
    required this.title,
    required this.value,
    required this.iconData,
  }) : super(key: key);

  // Helper methods for dynamic styling
  Color _getIconColor(String title) {
    switch (title) {
      case 'Plate No':
        return Colors.blue.shade700;
      case 'Customer Data':
        return Colors.purple.shade700;
      case 'Order NO':
        return Colors.orange.shade700;
      case 'Order Status':
        return Colors.green.shade700;
      case 'Service Status':
        return Colors.teal.shade700;
      case 'Technician ID':
        return Colors.indigo.shade700;
      default:
        return Colors.blueGrey.shade700;
    }
  }

  Color _getStatusColor(String value) {
    if (value.contains('Completed')) return Colors.green.shade600;
    if (value.contains('Progress')) return Colors.blue.shade600;
    if (value.contains('Pending')) return Colors.orange.shade600;
    return Colors.grey.shade600;
  }

  String _getStatusText(String value) {
    // Customize this if you want, for now just return the value
    return value;
  }

  @override
  Widget build(BuildContext context) {
    final iconColor = _getIconColor(title);
    final statusColor = _getStatusColor(value);
    final statusText = _getStatusText(value);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Material(
        elevation: 1,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.grey.shade50, Colors.grey.shade100],
            ),
            border: Border.all(color: AppColors.primaryColor, width: 1),
          ),
          child: Stack(
            children: [
              // Background pattern decoration
              Positioned(
                right: 0,
                bottom: 0,
                child: Opacity(
                  opacity: 0.03,
                  child: Icon(iconData, size: 80, color: Colors.blue.shade800),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // Icon container with modern look
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: iconColor.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Icon(iconData, size: 24, color: iconColor),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Text content
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title.toUpperCase(),
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.5,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            value,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Status indicator or action icon
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: statusColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: statusColor,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            statusText,
                            style: TextStyle(
                              fontSize: 12,
                              color: statusColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
