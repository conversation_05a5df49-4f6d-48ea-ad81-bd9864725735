import 'package:flutter/material.dart';
import 'package:technician_mobile_app/presentation/screens/splash_screen.dart';
import 'package:technician_mobile_app/services/local_data.dart';

class TechnicianMobileFlutter extends StatelessWidget {
  const TechnicianMobileFlutter({super.key});

  @override
  Widget build(BuildContext context) {
    CacheHelper cacheHelper = CacheHelper();
    print("The Device ID is: ${cacheHelper.getDataString(key: "device_id")}");
    // Set the preferred orientations to portrait only

    return MaterialApp(home: SplashScreen(), debugShowCheckedModeBanner: false);
  }
}
