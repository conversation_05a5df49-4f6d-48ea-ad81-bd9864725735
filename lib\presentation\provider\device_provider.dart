import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../domain/usecases/get_device_id.dart';
import '../../services/local_data.dart'; // Adjust path

class <PERSON>ceProvider extends ChangeNotifier {
  final GetDeviceId getDeviceIdUseCase;
  final CacheHelper cacheHelper;

  DeviceProvider(this.getDeviceIdUseCase, this.cacheHelper);

  String? deviceId;
  bool isLoading = false;
  String? error;

  Future<void> fetchDeviceId() async {
    isLoading = true;
    error = null;
    notifyListeners();

    try {
      deviceId = await getDeviceIdUseCase();
      debugPrint("✅ Device ID fetched: $deviceId");

      if (deviceId != null) {
        await cacheHelper.saveData(key: "device_id", value: deviceId);
        debugPrint("💾 Device ID saved locally.");
      }
    } catch (e) {
      error = e.toString();
      debugPrint("❌ Error fetching device ID: $error");
    }

    isLoading = false;
    notifyListeners();
  }

  /// Load device ID from cache (optional helper method)
  void loadCachedDeviceId() {
    deviceId = cacheHelper.getDataString(key: "device_id");
    debugPrint("📦 Loaded cached device ID: $deviceId");
    notifyListeners();
  }

  Future<void> exitApp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear(); // Clear all saved preferences
      debugPrint("🗑️ SharedPreferences cleared.");
    } catch (e) {
      debugPrint("❌ Error clearing SharedPreferences: $e");
    }
    SystemNavigator.pop(); // Exit the app
  }
}
