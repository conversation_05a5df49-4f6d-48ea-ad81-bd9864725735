import 'package:flutter/material.dart';
import 'package:technician_mobile_app/core/constants/app_colors.dart';

class BottomActionButtons extends StatelessWidget {
  final VoidCallback onAssign;
  final VoidCallback onGetDeviceId;
  final VoidCallback onExit;

  const BottomActionButtons({
    Key? key,
    required this.onAssign,
    required this.onGetDeviceId,
    required this.onExit,
  }) : super(key: key);

  Widget _iconButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    Color? color,
  }) {
    color ??= AppColors.primaryColor;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            shape: const CircleBorder(),
            padding: const EdgeInsets.all(14),
            elevation: 4,
            backgroundColor: color,
          ),
          child: Icon(icon, size: 26, color: Colors.white),
        ),
        const SizedBox(height: 6),
        Text(
          label,
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        border: Border(
          top: BorderSide(color: AppColors.primaryColor, width: 1),
        ),
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(0, -3),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _iconButton(
            icon: Icons.assignment_turned_in_rounded,
            label: 'Assign',
            onPressed: onAssign,
          ),
          _iconButton(
            icon: Icons.devices_rounded,
            label: 'Get Device ID',
            onPressed: onGetDeviceId,
          ),
          _iconButton(
            icon: Icons.exit_to_app_rounded,
            label: 'Exit',
            onPressed: onExit,
            color: Colors.redAccent,
          ),
        ],
      ),
    );
  }
}
