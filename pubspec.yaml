name: technician_mobile_app
description: "A new Flutter project."

publish_to: 'none' # Remove this line if you wish to publish to pub.dev


version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_launcher_icons: ^0.14.4
  device_info_plus: ^11.5.0
  provider: ^6.1.5
  shared_preferences: ^2.5.3

dev_dependencies:
  flutter_test:
    sdk: flutter


  flutter_lints: ^5.0.0


flutter:


  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
  #    - images/a_dot_ham.jpeg


flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.png"
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
