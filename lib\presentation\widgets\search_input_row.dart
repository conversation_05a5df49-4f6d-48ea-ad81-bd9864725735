import 'package:flutter/material.dart';

import '../../core/constants/app_colors.dart';

class SearchInputRow extends StatelessWidget {
  SearchInputRow({
    super.key,
    required this.title,
    required this.hintText,
    required this.controller,
    this.onPressed,
  });

  final String title;
  final String hintText;
  final TextEditingController controller;
  void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          title,
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: TextFormField(
            cursorColor: Colors.black54,
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(
                vertical: 12,
                horizontal: 16,
              ),
              hintText: hintText,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade400),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primaryColor, width: 2),
              ),
              suffixIcon: IconButton(
                icon: Icon(Icons.search),
                onPressed: onPressed,
                tooltip: 'Search',
              ),
            ),
            keyboardType: TextInputType.text,
          ),
        ),
      ],
    );
  }
}
