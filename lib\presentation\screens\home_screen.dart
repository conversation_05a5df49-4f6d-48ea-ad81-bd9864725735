import 'package:flutter/material.dart';
import 'package:technician_mobile_app/core/constants/app_colors.dart';

import '../../core/utils/spacing.dart';
import 'customer_screen.dart';
import 'fleet_screen.dart' hide verticalSpace;

class HomeScreen extends StatelessWidget {
  HomeScreen({Key? key}) : super(key: key);

  static String id = '/home';

  final List<Widget> screens = [FleetScreen(), CustomerScreen()];

  // Helper widget for icon button + label
  Widget _buildIconButton({
    required VoidCallback onPressed,
    required IconData icon,
    required String label,
    Color? color,
  }) {
    color ??= AppColors.primaryColor;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            shape: const CircleBorder(),
            padding: const EdgeInsets.all(16),
            elevation: 6,
            backgroundColor: color,
          ),
          child: Icon(icon, size: 28, color: Colors.white),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: DefaultTabController(
        length: screens.length,
        child: Scaffold(
          backgroundColor: AppColors.backgroundColor,
          body: SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                verticalSpace(20),

                // Page title
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Text(
                    'Welcome Back 👋',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryColor,
                    ),
                  ),
                ),

                verticalSpace(20),

                // TabBar
                TabBar(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  indicator: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  indicatorPadding: const EdgeInsets.symmetric(vertical: 6),
                  labelPadding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.black87,
                  labelStyle: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  indicatorColor: Colors.transparent,
                  tabs: const [
                    Tab(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8),
                        child: Text('Fleet'),
                      ),
                    ),
                    Tab(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8),
                        child: Text('Customer'),
                      ),
                    ),
                  ],
                ),

                verticalSpace(20),

                // Main content
                Expanded(child: TabBarView(children: screens)),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
