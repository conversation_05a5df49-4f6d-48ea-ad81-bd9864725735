import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../provider/device_provider.dart';
import '../widgets/bottom_action_button.dart';
import '../widgets/info_card.dart';
import '../widgets/search_input_row.dart';

class FleetScreen extends StatefulWidget {
  const FleetScreen({Key? key}) : super(key: key);

  @override
  State<FleetScreen> createState() => _FleetScreenState();
}

class _FleetScreenState extends State<FleetScreen> {
  final TextEditingController _plateController = TextEditingController();

  final List<MapEntry<String, Map<String, dynamic>>> dataEntries = [
    MapEntry("Plate No", {"value": "ABC1234", "icon": Icons.directions_car}),
    MapEntry("Customer Data", {"value": "John Doe", "icon": Icons.person}),
    MapEntry("Order NO", {
      "value": "ORD456",
      "icon": Icons.confirmation_number,
    }),
    MapEntry("Order Status", {"value": "In Progress", "icon": Icons.timelapse}),
    MapEntry("Service Status", {
      "value": "Completed",
      "icon": Icons.check_circle_outline,
    }),
    MapEntry("Technician ID", {"value": "TECH789", "icon": Icons.badge}),
  ];

  @override
  void dispose() {
    _plateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final deviceIdProvider = context.watch<DeviceProvider>();
    return Column(
      children: [
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            itemCount: dataEntries.length + 1,
            itemBuilder: (context, index) {
              if (index == 0) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 10),
                  child: SearchInputRow(
                    title: 'Plate No.',
                    hintText: 'Enter plate number',
                    controller: _plateController,
                    onPressed: () {
                      print(
                        'Search pressed with input: ${_plateController.text}',
                      );
                    },
                  ),
                );
              } else {
                final entry = dataEntries[index - 1];
                return InfoCard(
                  title: entry.key,
                  value: entry.value['value'] as String,
                  iconData: entry.value['icon'] as IconData,
                );
              }
            },
          ),
        ),
        BottomActionButtons(
          onAssign: () => print('Assign pressed'),
          onGetDeviceId: () => deviceIdProvider.fetchDeviceId(),
          onExit: () => deviceIdProvider.exitApp(),
        ),
      ],
    );
  }
}
