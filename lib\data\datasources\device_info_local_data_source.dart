import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';

abstract class DeviceInfoLocalDataSource {
  Future<String> getDeviceId();
}

class DeviceInfoLocalDataSourceImpl implements DeviceInfoLocalDataSource {
  final DeviceInfoPlugin deviceInfoPlugin;

  DeviceInfoLocalDataSourceImpl(this.deviceInfoPlugin);

  @override
  Future<String> getDeviceId() async {
    if (Platform.isAndroid) {
      final androidInfo = await deviceInfoPlugin.androidInfo;
      // Use androidId for a unique device identifier
      return androidInfo.id;
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfoPlugin.iosInfo;
      return iosInfo.identifierForVendor ?? "UNKNOWN_IOS_ID";
    } else {
      return "UNSUPPORTED_PLATFORM";
    }
  }
}
